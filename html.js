let a = {
  oTitle: document.title,
  list: [
    ...document
      .querySelector(".client-only-placeholder.editormd-preview")
      .querySelectorAll("img"),
  ].map((img) => img.src),
  video: [...document.querySelectorAll(".dplayer")].map(
    (v) =>
      JSON.parse(v.getAttribute("config")).video.url.match(/[0-9a-z]+\.m3u8/)[0]
  ),
  url: [...document.querySelectorAll(".dplayer")].map(
    (v) => JSON.parse(v.getAttribute("config")).video.url
  ),
  title: document.title,
};
console.log(a);
function copyTextToClipboard(text) {
  var textArea = document.createElement("textarea");
  textArea.value = text;
  document.body.appendChild(textArea);
  textArea.select();
  document.execCommand("copy");
  document.body.removeChild(textArea);
}
// 将a写入剪贴板
copyTextToClipboard(JSON.stringify(a));
