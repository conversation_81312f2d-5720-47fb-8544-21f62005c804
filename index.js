const fs = require("fs");
fs.readdirSync("./m3u8").forEach((m) => {
  let txt = fs.readFileSync(`./m3u8/${m}`, "utf8");
  txt = txt.replace(
    /URI\=\".+\"/,
    `URI="../crypt/${m.replace(".m3u8", ".key")}"`
  );
  fs.writeFileSync(`./m3u8/${m}`, txt, "utf8");
  fs.writeFileSync(
    `./bat/${m.replace(".m3u8", ".bat")}`,
    `ffmpeg -allowed_extensions ALL -protocol_whitelist "file,http,crypto,tcp,https,tls" -i C:/node-ts/m3u8/${m} -c copy C:/node-ts/videos/${m.replace(
      ".m3u8",
      ".mp4"
    )}`,
    "utf8"
  );
  fs.appendFileSync(
    `./all.bat`,
    `ffmpeg -allowed_extensions ALL -protocol_whitelist "file,http,crypto,tcp,https,tls" -i C:/node-ts/m3u8/${m} -c copy C:/node-ts/videos/${m.replace(
      ".m3u8",
      ".mp4"
    )}\n`,
    "utf8"
  );
});
