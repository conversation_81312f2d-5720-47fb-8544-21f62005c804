import fs from "fs";
import fetch from "node-fetch";
import { execSync } from "child_process";
fs.readdirSync("./m3u8").forEach(async (m) => {
  let flag = false;
  let txt = fs.readFileSync(`./m3u8/${m}`, "utf8");
  let crypt = txt.match(/URI\=\".+\"/)[0].replaceAll(/URI\=\"|\"/g, "");
  if (!fs.existsSync(`./crypt/${m.replace(".m3u8", ".key")}`)) {
    flag = true;
    const response = await fetch(crypt);
    const buffer = await response.arrayBuffer();
    fs.writeFileSync(
      `./crypt/${m.replace(".m3u8", ".key")}`,
      Buffer.from(buffer)
    );
  }

  txt = txt.replace(
    /URI\=\".+\"/,
    `URI="../crypt/${m.replace(".m3u8", ".key")}"`
  );
  fs.writeFileSync(`./m3u8/${m}`, txt, "utf8");
  fs.writeFileSync(
    `./bat/${m.replace(".m3u8", ".bat")}`,
    `ffmpeg -allowed_extensions ALL -protocol_whitelist "file,http,crypto,tcp,https,tls" -i C:/node-ts/m3u8/${m} -c copy C:/node-ts/videos/${m.replace(
      ".m3u8",
      ".mp4"
    )}`,
    "utf8"
  );
  fs.appendFileSync(
    `./all.bat`,
    `ffmpeg -allowed_extensions ALL -protocol_whitelist "file,http,crypto,tcp,https,tls" -i C:/node-ts/m3u8/${m} -c copy C:/node-ts/videos/${m.replace(
      ".m3u8",
      ".mp4"
    )}\n`,
    "utf8"
  );
  // 等待shell执行完成并持续打印shell输出流
  if (flag) {
    execSync(
      `ffmpeg -allowed_extensions ALL -protocol_whitelist "file,http,crypto,tcp,https,tls" -i C:/node-ts/m3u8/${m} -c copy C:/node-ts/videos/${m.replace(
        ".m3u8",
        ".mp4"
      )}`,
      { stdio: "inherit" }
    );
  }
});
