import fs from "fs";
import fetch from "node-fetch";
import { execSync } from "child_process";
// node读取剪贴板
let clipboard = ""
// try {
//   clipboard = execSync("powershell Get-Clipboard").toString();
// } catch (error) {
//   console.log("剪贴板获取失败");
// }
let json = null
try {
  json = JSON.parse(clipboard);
} catch (error) {
  console.log("剪贴板中没有数据");
  try {
    json = JSON.parse(fs.readFileSync("./1.json", "utf8"));
  } catch (error) {
    json = null
    console.log("./1.json中也没有数据");
  }
}
if (!json) {
  throw new Error("没有数据");
}
if (!fs.existsSync(`./images/${json.title}`)) {
  fs.mkdirSync(`./images/${json.title}`);
}
// 保存base64图片
json.list.forEach((img, i) => {
  // 去除base64数据URL前缀（如果存在）
  let base64Data = img;
  if (img.startsWith("data:image/")) {
    base64Data = img.split(",")[1];
  }

  fs.writeFileSync(`./images/${json.title}/${i}.jpg`, base64Data, "base64");
});

json.url.forEach(async (u, i) => {
  let response = await fetch(u);
  let buffer = await response.arrayBuffer();
  fs.writeFileSync(`./m3u8/${json.video[i]}`, Buffer.from(buffer));

  let txt = fs.readFileSync(`./m3u8/${json.video[i]}`, "utf8");
  let crypt = txt.match(/URI\=\".+\"/)[0].replaceAll(/URI\=\"|\"/g, "");
  if (!fs.existsSync(`./crypt/${json.video[i].replace(".m3u8", ".key")}`)) {
    const response = await fetch(crypt);
    const buffer = await response.arrayBuffer();
    fs.writeFileSync(
      `./crypt/${json.video[i].replace(".m3u8", ".key")}`,
      Buffer.from(buffer)
    );
  }

  txt = txt.replace(
    /URI\=\".+\"/,
    `URI="../crypt/${json.video[i].replace(".m3u8", ".key")}"`
  );
  fs.writeFileSync(`./m3u8/${json.video[i]}`, txt, "utf8");
  fs.writeFileSync(
    `./bat/${json.video[i].replace(".m3u8", ".bat")}`,
    `ffmpeg -allowed_extensions ALL -protocol_whitelist "file,http,crypto,tcp,https,tls" -i C:/node-ts/m3u8/${
      json.video[i]
    } -c copy C:/node-ts/videos/${json.video[i].replace(".m3u8", ".mp4")}`,
    "utf8"
  );
  fs.appendFileSync(
    `./all.bat`,
    `ffmpeg -allowed_extensions ALL -protocol_whitelist "file,http,crypto,tcp,https,tls" -i C:/node-ts/m3u8/${
      json.video[i]
    } -c copy C:/node-ts/videos/${json.video[i].replace(".m3u8", ".mp4")}\n`,
    "utf8"
  );

  if (!fs.existsSync(`./images/${json.title}/${json.video[i].replace(".m3u8", ".mp4")}`)) {
    // 等待shell执行完成并持续打印shell输出流
    execSync(
      `ffmpeg -allowed_extensions ALL -protocol_whitelist "file,http,crypto,tcp,https,tls" -i C:/node-ts/m3u8/${
        json.video[i]
      } -c copy C:/node-ts/videos/${json.video[i].replace(".m3u8", ".mp4")}`,
      { stdio: "inherit" }
    );
    fs.renameSync(
      `./videos/${json.video[i].replace(".m3u8", ".mp4")}`,
      `./images/${json.title}/${json.video[i].replace(".m3u8", ".mp4")}`
    );
  }
});
